<template>
  <div class="wrapper">
    <!-- 顶部信息区域 -->
    <div class="top-card">
      <div class="top-title">收银台</div>
      <div class="top-amount">
        ¥
        <span class="top-amount-num">{{
          cashierParams.price | unitPrice
        }}</span>
      </div>
      <div class="top-timer" v-if="!routerVal.after_sn">支付剩余时间：
        <u-count-down :show-days="false" :show-border="false" font-size="28" color="#000" border-color="#fff" ref="uCountDown" :timestamp="autoCancel" @end="onCountdownEnd"></u-count-down>
      </div>
    </div>

    <!-- 先享卡额度支付模块 -->
    <div class="enjoy-card-section" v-if="shouldShowEnjoyCardSection">
      <div
        style="
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        "
      >
        <div>
          <div style="font-size: 32rpx; font-weight: 500; color: #333">
            臻享卡额度支付
          </div>
          <div style="font-size: 24rpx; color: #999; margin-top: 6rpx">
            先享分期额度{{enjoyCardInfo.creditAmount}}元
          </div>
        </div>
        <div class="pay-item-check">
          <span
            v-if="selectedPay === 'ENJOY_CARD'"
            class="pay-check-circle checked"
          ></span>
          <span v-else class="pay-check-circle" @click="selectEnjoyCard"></span>
        </div>
      </div>
      <div style="display: flex; gap: 24rpx; margin-top: 14rpx">
        <div
          v-for="(item, idx) in enjoyCardStages"
          :key="item.term"
          @click="selectEnjoyCardStage(idx)"
          :class="[
            'enjoy-card-stage',
            {
              active:
                selectedPay === 'ENJOY_CARD' && enjoyCardSelectedStage === idx,
            },
          ]"
        >
          <div class="enjoy-card-amount">
            <template v-if="item.preInfo">
              首期¥{{ item.preInfo.periodsList[0].amount }}
            </template>
            <template v-else>
              &nbsp;
            </template>
          </div>
          <div class="enjoy-card-term">{{ item.term }}期</div>
          <div
            v-if="selectedPay === 'ENJOY_CARD' && enjoyCardSelectedStage === idx && item.term === 6"
            class="enjoy-card-rate"
          >
            推荐
          </div>
        </div>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          margin-top: 20rpx;
          font-size: 24rpx;
          color: #999;
        "
      >
        <div>还款计划</div>
        <div @click="showCreditModal = true" style="cursor: pointer">
          查看分期明细
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </div>
      </div>
    </div>

    <!-- 支付方式选择 -->
    <div class="pay-section" v-if="cashierParams.price > 0">
      <!-- <div class="pay-title">请选择支付方式</div> -->
      <div
        class="other-pay-toggle"
        @click="shouldShowEnjoyCardSection && (showOtherPay = !showOtherPay)"
      >
        <span>{{
          shouldShowEnjoyCardSection ? "其他支付方式" : "请选择支付方式"
        }}</span>
        <u-icon
          v-if="shouldShowEnjoyCardSection"
          :name="showOtherPay ? 'arrow-up' : 'arrow-down'"
          size="24"
          color="#333"
        ></u-icon>
      </div>
      <div v-if="!shouldShowEnjoyCardSection || showOtherPay" class="pay-list">
        <div
          class="pay-item-card"
          v-for="(item, index) in payList"
          :key="index"
          :class="{ selected: selectedPay === item }"
          @click="selectPay(item)"
        >
          <div class="pay-item-left">
            <u-icon
              v-if="item == 'WECHAT'"
              name="weixin-circle-fill"
              color="#00c98b"
              size="58"
            ></u-icon>
            <u-icon
              v-if="item == 'ALIPAY'"
              name="zhifubao-circle-fill"
              color="#008ffa"
              size="58"
            ></u-icon>
          </div>
          <div class="pay-item-name">
            <span v-if="item == 'WECHAT'">微信支付</span>
            <span v-if="item == 'ALIPAY'">支付宝支付</span>
            <span v-if="item == 'WALLET'"
              >余额支付(当前余额：¥{{ walletValue | unitPrice }})</span
            >
          </div>
          <div class="pay-item-check">
            <span
              v-if="selectedPay === item"
              class="pay-check-circle checked"
            ></span>
            <span v-else class="pay-check-circle"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部确认支付按钮 -->
    <div class="pay-btn-box">
      <template v-if="selectedPay === 'ENJOY_CARD'">
        <div class="protocol-check">
          <u-checkbox
            v-model="agreeProtocol"
            shape="circle"
            active-color="#ff6b35"
            icon-size="24"
            label-size="24"
            style="margin-right: 8rpx"
          >
            <span>我已阅读并同意</span>
            <span class="protocol-link" @click.stop="openProtocol"
              >《借款相关协议》</span
            >
          </u-checkbox>
        </div>
        <template v-if="enjoyCardAuditIng">
          <button class="pay-btn-gradient evaluating-btn" disabled>
            额度评估中
            <span class="loading-dot"></span>
          </button>
        </template>
        <template v-else-if="enjoyCardReApply">
          <button class="pay-btn-gradient evaluating-btn" @click="onApplyAnew">
            重新申请后支付
          </button>
        </template>
        <template v-else>
          <button class="pay-btn-gradient evaluating-btn" @click="onApplyCredit">
            {{ enjoyCardPayText }}
          </button>
        </template>
      </template>
      <template v-else>
        <button class="pay-btn-gradient" @click="onConfirmPay">确认支付</button>
      </template>
      <div class="security-tip">
        <image src="/static/financial/desc.png" mode="scaleToFill" />
        平台承诺保障您的信息安全
      </div>
    </div>
    <div v-if="showLeaveModal" class="leave-modal-mask">
      <div class="leave-modal-box">
        <div class="leave-modal-title">确认放弃支付么？</div>
        <div class="leave-modal-desc">
          {{
            routerVal.after_sn
              ? "违约金在" +
                leaveModalTimeText +
                "内未完成将被取消，请尽快完成支付！"
              : "订单在" +
                leaveModalTimeText +
                "内未完成将被取消，请尽快完成支付！"
          }}
        </div>
        <div class="leave-modal-btns">
          <div class="leave-modal-btn cancel" @click="giveUpPay">放弃</div>
          <div class="leave-modal-btn confirm" @click="showLeaveModal = false">
            继续支付
          </div>
        </div>
      </div>
    </div>
    <u-popup v-model="showCreditModal" mode="bottom" border-radius="16">
      <div class="credit-modal">
        <div class="credit-modal-header">
          <span>分期明细</span>
          <u-icon
            name="close"
            size="32"
            color="#999"
            @click="showCreditModal = false"
            style="cursor: pointer"
          />
        </div>
        <div class="credit-modal-summary">
          <div>还款{{ selectedStagingInfo ? selectedStagingInfo.periods : '...' }}期，总还款金额(元)</div>
          <div class="credit-modal-amount">{{ selectedStagingInfo ? selectedStagingInfo.repayment : '...' }}</div>
          <div class="credit-modal-rate">综合年化利率{{ selectedStagingInfo ? (selectedStagingInfo.interestRate * 100).toFixed(2) : '...' }}%</div>
        </div>
        <scroll-view scroll-y="true" style="height: 600rpx">
          <div class="credit-modal-list">
            <div class="credit-modal-item" v-for="item in (selectedStagingInfo ? selectedStagingInfo.periodsList : [])" :key="item.nper">
              <div class="credit-modal-item-left">
                <div class="credit-modal-item-title">
                  {{ item.nper === 1 ? "首期" : item.nper + "期" }}
                </div>
                <div class="credit-modal-item-date">{{ item.gmtPlanRepay.split(' ')[0] }}</div>
              </div>
              <div class="credit-modal-item-timeline">
                <div class="credit-dot"></div>
                <div v-if="item.nper < selectedStagingInfo.periodsList.length" class="credit-line"></div>
              </div>
              <div class="credit-modal-item-right">
                <div class="credit-modal-item-amount">¥{{ item.amount }}</div>
                <div class="credit-modal-item-detail">
                  本金¥{{ (item.amount - item.interestFee).toFixed(2) }}+息费¥{{ item.interestFee.toFixed(2) }}
                </div>
              </div>
            </div>
          </div>
        </scroll-view>
      </div>
    </u-popup>
    
    <!-- 验证码弹窗 -->
    <u-popup v-model="showSms" mode="center" border-radius="16" width="600rpx">
      <view class="sms-modal">
        <view class="sms-header">
          <text class="sms-title">短信验证</text>
          <u-icon name="close" size="32" @click="showSms = false" style="position:absolute;right:0;top:0;"></u-icon>
        </view>
        <view class="sms-desc">验证码将发送至手机{{ phone }}，请注意查收</view>
        <view class="sms-row">
          <input class="sms-input" v-model="smsCode" maxlength="6" placeholder="请输入验证码" placeholder-class="sms-placeholder" />
          <text class="sms-get" :class="{disabled: smsCountdown>0}" @click="getSmsCode">{{ smsCountdown>0 ? smsCountdown+'s后重发' : '获取验证码' }}</text>
        </view>
        <button class="sms-btn" @click="submitSms">确认</button>
      </view>
    </u-popup>
  </div>
</template>
<script>
import * as API_Trade from "@/api/trade";
import { payCallback } from "@/api/members";
import { sendMobile_Can } from '@/api/login';
import { mapState,mapMutations } from 'vuex';
import { checkEnjoyCardStatus, getLoanStagingPreInfo } from "@/api/common.js";
export default {
  data() {
    return {
      //路径传参
      routerVal: "",
      //收银台参数
      cashierParams: "",
      //支付方式集合
      payList: "",
      //支付sn
      sn: "",
      //订单类型
      orderType: "",
      //支付异常
      exception: {},
      //支付表单
      payForm: {},
      //支付类型 APP/WECHAT_MP/H5/NATIVE app/微信小程序/h5/二维码
      paymentType: "",
      // 支付客户端 APP/NATIVE/JSAPI/H5
      paymentClient: "",
      //余额
      walletValue: 0.0,
      // 支付倒计时
      autoCancel: 0,
      selectedPay: "", // 新增，当前选中的支付方式
      showLeaveModal: false, // 控制挽留弹窗
      // 先享卡相关
      enjoyCardStages: [
        { term: 6, preInfo: null },
        { term: 3, preInfo: null },
      ],
      enjoyCardSelectedStage: 0, // 默认选中第一个分期
      showOtherPay: false,
      isEvaluating: false, // 额度评估中
      agreeProtocol: false, // 是否同意协议
      showCreditModal: false, // 新增，控制分期明细弹窗
      // 验证码相关
      showSms: false,
      smsCode: '',
      smsCountdown: 0,
      smsTimer: null,
    };
  },
  onLoad(val) {
    this.routerVal = val;

    //初始化参数
    // #ifdef APP-PLUS
    this.paymentType = "APP";
    this.paymentClient = "APP";
    //#endif
    // #ifdef MP-WEIXIN
    this.paymentType = "WECHAT_MP";
    this.paymentClient = "MP";
    //#endif
    // #ifdef H5
    this.paymentType = "H5";
    //如果是微信浏览器，则使用公众号支付，否则使用h5，
    // 区别是：h5是通过浏览器外部调用微信app进行支付，而JSAPI则是 在微信浏览器内部，或者小程序 调用微信支付
    this.paymentClient = this.isWeiXin() ? "JSAPI" : "H5";
    //#endif

    //
  },
  onBackPress(e) {
    if (e.from == "backbutton") {
      this.showLeaveModal = true;
      return true; // 阻止默认返回行为
    }
  },
  beforeDestroy() {
    clearInterval(this.smsTimer);
  },
  mounted() {
    this.cashierData();
    this.checkEnjoyCardStatus();
    // this.selectedPay = "ENJOY_CARD";
    this.enjoyCardSelectedStage = 0;
  },
  onShow() {
    // this.checkEnjoyCardStatus();
  },
  methods: {
    ...mapMutations(["login", "setEnjoyCardInfo"]),
    // 获取所有分期预信息
    async fetchAllStagingPreInfo() {
      if (!this.cashierParams || !this.cashierParams.price) return;

      const promises = this.enjoyCardStages.map(async (stage, index) => {
        try {
          const params = {
            amount: this.cashierParams.price,
            periods: stage.term
          };
          const res = await getLoanStagingPreInfo(params);
          if (res.data.success) {
            this.$set(this.enjoyCardStages[index], 'preInfo', res.data.result);
          } else {
            this.$set(this.enjoyCardStages[index], 'preInfo', null);
          }
        } catch (error) {
          this.$set(this.enjoyCardStages[index], 'preInfo', null);
          console.error(`获取${stage.term}期分期信息失败:`, error);
        }
      });
      
      await Promise.all(promises);
    },
    // 新增：检查臻享卡显示状态
    async checkEnjoyCardStatus() {
      try {
        const res = await checkEnjoyCardStatus();
        if (res && res.data) {
          // 存储整个data对象
          console.log(res.data);
          const obj = {
            desc:'臻享卡',
            hasIngLoan:false,
            showInst:true,
            login:true,
            showMy:false,
            status:'PASS_PAY',
            creditAmount:10000,
            toRepayAmount:1000,
            overdueDays:5,
            desc:'8月20号重新申请'
          }
          this.setEnjoyCardInfo(obj);
          // this.setEnjoyCardInfo(res.data.data);
        } else {
          // 接口失败时设为null
          this.setEnjoyCardInfo(null);
        }
      } catch (error) {
        console.error('检查臻享卡显示状态失败:', error);
        this.setEnjoyCardInfo(null);
      }
      if (this.shouldShowEnjoyCardSection) {
        this.selectedPay = "ENJOY_CARD";
      } else {
        this.selectedPay = "";
      }
    },
    /**
     * 支付成功后跳转
     */
    callback(paymentMethod) {
      uni.navigateTo({
        url:
          "/pages/cart/payment/success?paymentMethod=" +
          paymentMethod +
          "&payPrice=" +
          this.cashierParams.price +
          "&orderType=" +
          this.orderType,
      });
    },
   
    /**
     * 获取收银详情
     */
    cashierData() {
      let parms = {};

      if (this.routerVal.recharge_sn) {
        // 判断当前是否是充值
        this.sn = this.routerVal.recharge_sn;
        this.orderType = "RECHARGE";
      } else if (this.routerVal.trade_sn) {
        this.sn = this.routerVal.trade_sn;
        this.orderType = "TRADE";
      } else if (this.routerVal.after_sn) {
        this.sn = this.routerVal.after_sn;
        this.orderType = "PENALTY";
      } else {
        this.sn = this.routerVal.order_sn;
        this.orderType = "ORDER";
      }
      parms.sn = this.sn;
      parms.orderType = this.orderType;
      parms.clientType = this.paymentType;

      API_Trade.getCashierData(parms).then((res) => {
        if (res.data.success) {
          this.cashierParams = res.data.result;
          this.fetchAllStagingPreInfo(); // 获取所有分期信息

          // #ifdef MP-WEIXIN
          this.payList = res.data.result.support.filter((item) => {
            return item != "ALIPAY";
          });
          // #endif

          if (this.routerVal.recharge_sn) {
            this.payList = res.data.result.support.filter((item) => {
              return item != "WALLET";
            });
          } else {
            this.payList = res.data.result.support;
          }
          // #ifdef H5
          //判断是否微信浏览器
          var ua = window.navigator.userAgent.toLowerCase();
          if (ua.match(/MicroMessenger/i) == "micromessenger") {
            this.payList = res.data.result.support.filter((item) => {
              return item != "ALIPAY";
            });
            // 充值的话仅保留微信支付
            if (this.orderType == "RECHARGE") {
              this.payList = res.data.result.support.filter((item) => {
                return item == "WECHAT";
              });
            }
          }
          // #endif

          this.walletValue = res.data.result.walletValue;
          this.autoCancel =
            (res.data.result.autoCancel - new Date().getTime()) / 1000;
        } else if (res.data.code == 32000) {
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/order/myOrder?status=0`,
            });
          }, 500);
        }
      });
    },

    awaitPay(payment) {
      this.$u.throttle(() => {
        this.pay(payment);
      }, 2000);
    },

    //订单支付
    async pay(payment) {
      // 支付编号
      const sn = this.sn;
      // 交易类型【交易号|订单号】
      const orderType = this.orderType;

      const clientType = this.paymentType;
      let params = {
        sn,
        orderType,
        clientType,
      };

      //支付方式 WECHAT/ALIPAY
      const paymentMethod = payment;
      // 客户端类型 APP/NATIVE/JSAPI/H5
      const paymentClient = this.paymentClient;

      uni.showLoading({
        title: "正在唤起支付...",
        mask: true,
      });

      // #ifdef APP-PLUS
      //APP pay
      // 初始化支付签名
      await API_Trade.initiatePay(paymentMethod, paymentClient, params).then(
        (signXml) => {
          if (this.$store.state.isShowToast) {
            uni.hideLoading();
          }
          //如果支付异常
          if (!signXml.data.success) {
            uni.showToast({
              title: signXml.data.message,
              duration: 2000,
            });
            return;
          }

          let payForm = signXml.data.result;

          let paymentType = paymentMethod === "WECHAT" ? "wxpay" : "alipay";

          if (paymentMethod === "WALLET") {
            uni.showToast({
              icon: "none",
              title: "支付成功!",
            });
            this.callback(paymentMethod);
          } else {
            uni.requestPayment({
              provider: paymentType,
              orderInfo: payForm || "",
              success: (e) => {
                uni.showToast({
                  icon: "none",
                  title: "支付成功!",
                });
                this.callback(paymentMethod);
              },
              fail: (e) => {
                console.log(this);
                this.exception = e;

                uni.showToast({
                  title: "支付失败,如果您已支付，请勿反复支付",
                  icon: "none",
                  duration: 1000,
                });
                // uni.showModal({
                // 	content: "支付失败,如果您已支付，请勿反复支付",
                // 	showCancel: false,
                // });
              },
            });
          }
        }
      );
      //APP pay
      // #endif

      //#ifdef H5
      //H5 pay
      await API_Trade.initiatePay(paymentMethod, paymentClient, params).then(
        (res) => {
          let response = res.data;
          //如果非支付宝支付才需要进行判定，因为支付宝h5支付是直接输出的，没有返回所谓的消息状态
          if (paymentMethod !== "ALIPAY") {
            //如果支付异常
            if (!response.success) {
              uni.showToast({
                title: response.message,
                duration: 2000,
                icon: "none",
              });
              return;
            }
          }
          if (paymentMethod === "ALIPAY") {
            document.write(response);
          } else if (paymentMethod === "WECHAT") {
            if (this.isWeiXin()) {
              //微信公众号支付
              WeixinJSBridge.invoke(
                "getBrandWCPayRequest",
                response.result,
                (res) => {
                  if (res.err_msg == "get_brand_wcpay_request:ok") {
                    // 使用以上方式判断前端返回,微信团队郑重提示：
                    //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
                    uni.showToast({
                      icon: "none",
                      title: "支付成功!",
                    });
                    this.callback(paymentMethod);
                  } else {
                    // uni.showModal({
                    // 	content: "支付失败,如果您已支付，请勿反复支付",
                    // 	showCancel: false,
                    // });
                    uni.showToast({
                      title: "支付失败,如果您已支付，请勿反复支付",
                      icon: "none",
                      duration: 1000,
                    });
                  }
                }
              );
              if (this.$store.state.isShowToast) {
                uni.hideLoading();
              }
            } else {
              window.location.href = JSON.parse(response.result).h5_url;
              const searchParams = {
                ...params,
                price: this.cashierParams,
              };
              const timer = setInterval(() => {
                payCallback(searchParams).then((res) => {
                  if (res.data.result) {
                    clearTimeout(timer);
                    uni.navigateTo({
                      url: "/pages/order/myOrder",
                    });
                  }
                });
              }, 3000);

              if (this.$store.state.isShowToast) {
                uni.hideLoading();
              }
            }
          } else if (paymentMethod === "WALLET") {
            uni.showToast({
              title: response.message,
              icon: "none",
            });
            if (response.success) {
              this.callback(paymentMethod);
            }
          }
        }
      );
      //H5pay
      // #endif

      //#ifdef MP-WEIXIN
      //微信小程序
      await API_Trade.initiatePay(paymentMethod, paymentClient, params).then(
        (res) => {
          let response = res.data.result;
          //如果支付异常
          if (!res.data.success) {
            uni.showModal({
              content: res.data.message,
              showCancel: false,
            });
            return;
          }
          if (paymentMethod === "WECHAT") {
            uni.requestPayment({
              provider: "wxpay",
              appid: response.appid,
              timeStamp: response.timeStamp,
              nonceStr: response.nonceStr,
              package: response.package,
              signType: response.signType,
              paySign: response.paySign,
              success: (e) => {
                console.log(e);
                uni.showToast({
                  icon: "none",
                  title: "支付成功!",
                });
                this.callback(paymentMethod);
              },
              fail: (e) => {
                console.log(e);
                this.exception = e;
                uni.showToast({
                  title: "支付失败,如果您已支付，请勿反复支付",
                  icon: "none",
                  duration: 1000,
                });
                // uni.showModal({
                // 	content: "支付失败,如果您已支付，请勿反复支付",
                // 	showCancel: false,
                // });
              },
            });
          } else {
            uni.showToast({
              icon: "none",
              title: "支付成功!",
            });
            this.callback(paymentMethod);
          }
        }
      );
      // #endif
    },
    isWeiXin() {
      var ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        return true;
      } else {
        return false;
      }
    },
    selectPay(item) {
      if (this.selectedPay === "ENJOY_CARD") {
        this.enjoyCardSelectedStage = 0;
      }
      this.selectedPay = item;
      this.isEvaluating = false;
    },
    onConfirmPay() {
      if (!this.selectedPay) {
        uni.showToast({ title: "请选择支付方式", icon: "none" });
        return;
      }
      if (this.selectedPay === "ENJOY_CARD") {
        // TODO: 这里处理先享卡支付逻辑，enjoyCardStages[this.enjoyCardSelectedStage] 可用
        uni.showToast({ title: "先享卡支付演示", icon: "none" });
        return;
      }
      this.awaitPay(this.selectedPay);
    },
    giveUpPay() {
      this.showLeaveModal = false;
      // if(this.routerVal.recharge_sn){
      // 	uni.switchTab({ url: '/pages/tabbar/user/my' });
      // } else {
      // 	uni.navigateTo({ url: "/pages/order/myOrder?status=0" });
      // }
      // 判断跳转目标
      if (this.routerVal.after_sn) {
        uni.redirectTo({
          url: "/pages/order/afterSales/afterSales",
        });
      } else {
        uni.redirectTo({
          url: "/pages/order/myOrder?status=0",
        });
      }
    },
    selectEnjoyCard() {
      this.selectedPay = "ENJOY_CARD";
      this.isEvaluating = true;
    },
    selectEnjoyCardStage(idx) {
      this.enjoyCardSelectedStage = idx;
      this.selectedPay = "ENJOY_CARD";
      this.isEvaluating = true;
    },
    toggleProtocol() {
      this.agreeProtocol = !this.agreeProtocol;
    },
    openProtocol() {
      // 跳转或弹窗
      console.log("打开协议");
    },
    onApplyCredit() {
      const info = this.$store.state.enjoyCardInfo;
      if (!this.agreeProtocol) {
        uni.showToast({ title: '请先同意协议', icon: 'none' });
        return;
      }
      if (info && info.status === 'LOGIN') {
        // 还没申请过额度，去申请
        uni.navigateTo({ url: '/pages/financial/idAuth' });
      } else {
        // 其它情况都弹短信验证
        this.showSms = true;
      }
    },
    onApplyAnew(){
      uni.navigateTo({
					url: '/pages/financial/faceAuth'
			});
    },
    async getSmsCode() {
      if (this.smsCountdown > 0) return;
      if (!this.rawPhone || !/^1[3-9]\d{9}$/.test(this.rawPhone)) {
        uni.showToast({ title: '手机号无效', icon: 'none' });
        return;
      }
      try {
        await sendMobile_Can('ENJOY_CARD');
        uni.showToast({ title: '验证码已发送', icon: 'none' });
        this.smsCountdown = 60;
        this.smsTimer = setInterval(() => {
          if (this.smsCountdown > 0) {
            this.smsCountdown--;
          } else {
            clearInterval(this.smsTimer);
          }
        }, 1000);
      } catch (e) {
        uni.showToast({ title: '验证码发送失败', icon: 'none' });
      }
    },
    submitSms() {
      if (!this.smsCode) {
        uni.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      if (!/^\d{6}$/.test(this.smsCode)) {
        uni.showToast({ title: '验证码格式错误', icon: 'none' });
        return;
      }
      console.log('提交验证码');
      
      // TODO: 这里调用先享卡额度申请API
      uni.navigateTo({
          url: `/pages/cart/payment/error`
      });
    },
    onCountdownEnd() {
				// 倒计时结束时触发的事件
				console.log('倒计时结束');
				// 可以在这里执行一些操作，例如提示用户支付超时
				uni.showToast({
					title: '支付超时，请重新支付',
					icon: 'none'
				});
				// 例如，跳转到订单列表或订单详情
				uni.navigateTo({
					url: '/pages/order/myOrder?status=0'
				});
		}
  },
  computed: {
    ...mapState(['userInfo', 'enjoyCardInfo']),
    selectedStagingInfo() {
      if (this.enjoyCardSelectedStage !== null && this.enjoyCardStages[this.enjoyCardSelectedStage]) {
        return this.enjoyCardStages[this.enjoyCardSelectedStage].preInfo
      }
      return null
    },
    phone() {
      const mobile = this.userInfo.mobile || this.userInfo.phone || '';
      if (!mobile) return '';
      // 脱敏处理
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },
    rawPhone() {
      return this.userInfo.mobile || this.userInfo.phone || '';
    },
    leaveModalTimeText() {
      // autoCancel 是秒数
      let seconds = Math.max(0, Math.floor(this.autoCancel));
      let min = Math.floor(seconds / 60);
      let sec = seconds % 60;
      // 返回"X分X秒"格式
      return `${min}分${sec < 10 ? "0" : ""}${sec}秒`;
      // 如果想要"mm:ss"格式，改成：
      // return `${min < 10 ? '0' : ''}${min}:${sec < 10 ? '0' : ''}${sec}`;
    },
    enjoyCardPayText() {
      // status为LOGIN时显示申请额度后支付，其它都显示立即支付
      const info = this.$store.state.enjoyCardInfo;
      if (info && info.status === 'LOGIN') {
        return '申请额度后支付';
      }
      return '立即支付';
    },
    enjoyCardAuditIng() {
      const info = this.$store.state.enjoyCardInfo;
      return info && info.status === 'AUDIT_ING';
    },
    enjoyCardReApply() {
      const info = this.$store.state.enjoyCardInfo;
      return info && (info.status === 'FAIL_OUT' || info.status === 'OVERDATE');
    },
    shouldShowEnjoyCardSection() {
      const info = this.enjoyCardInfo;
      return info && (info.showMy === true || info.hasIngLoan === true || info.showInst === true);
    },
  },
};
</script>
<style scoped lang="scss">
.method_icon {
  vertical-align: middle;
}

.method_name {
  font-size: 28rpx;
  color: #999;
  padding-left: 24rpx;
}

.row {
  display: flex;
  width: 100%;
}

/deep/ .u-row {
  width: 100% !important;
  display: flex;
  justify-content: space-between !important;
}

.method_name,
.col1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.col1 {
  text-align: center;
  flex: 99;
}

.col3 {
  text-align: right;
  flex: 1;
}

.payItem {
  padding: 13px 25rpx;
  border-top: 1px solid #f9f9f9;

  line-height: 100rpx;
  font-size: 36rpx;
  color: #333;
}

.ptips {
  font-size: 32rpx;
  margin: 20rpx 0;
  color: #333;

  > span {
    font-size: 40rpx;
    color: #df5a52;
    margin-left: 10rpx;
  }
}

.img {
  width: 392rpx !important;
  height: 296rpx !important;
}

.wrapper {
  min-height: 100vh;
  background: #f9f9f9;
  padding-bottom: 60rpx;
}

.top-card {
  background: #fff;
  border-radius: 20rpx;
  margin: 20rpx 32rpx 40rpx 32rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.03);
  text-align: center;
  padding: 40rpx 0 32rpx 0;
}
.top-title {
  font-weight: 400;
  font-size: 24rpx;
  color: #000000;
  margin-bottom: 16rpx;
}
.top-amount {
  font-size: 32rpx;
  color: #ff5134;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.top-amount-num {
  font-size: 56rpx;
  color: #ff5134;
  font-weight: bold;
}
.top-timer {
  font-size: 24rpx;
  color: #000000;
  margin-top: 40rpx;
}

.pay-section {
  width: 686rpx;
  margin: 20rpx auto 0;
  background: #fff;
  border-radius: 20rpx;
}
.pay-title {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  margin-top: 40rpx;
  margin-bottom: 28rpx;
  margin-left: 32rpx;
}
.pay-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  //   margin: 0 24rpx;
}
.pay-item-card {
  //   background: #fff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  height: 124rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.02);
  position: relative;
  transition: box-shadow 0.2s;
}
.pay-item-card.selected {
  //   box-shadow: 0 4rpx 24rpx 0 rgba(255,90,0,0.08);
}
.pay-item-left {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
}
.pay-item-name {
  font-size: 30rpx;
  color: #222;
  flex: 1;
}
.pay-item-check {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}
.pay-check-circle {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #e5e5e5;
  background: #fff;
  position: relative;
}
.pay-check-circle.checked {
  border: 2rpx solid #ff5a00;
  background: #ff5a00;
}
.pay-check-circle.checked::after {
  content: "";
  display: block;
  width: 10rpx;
  height: 18rpx;
  border: solid #fff;
  border-width: 0 4rpx 4rpx 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -60%) rotate(45deg);
  border-radius: 2rpx;
}

.pay-btn-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  padding-bottom: 20rpx;
  z-index: 10;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  justify-content: center;
  .pay-btn-gradient {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 86rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #ffffff;
    text-align: center;
    line-height: 100rpx;
    margin-top: 10rpx;
  }
  .security-tip {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #ff5134;
    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
.evaluating-btn {
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  font-size: 32rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-dot {
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #fff;
  border-top: 3rpx solid #ff9500;
  border-radius: 50%;
  margin-left: 16rpx;
  animation: spin 1s linear infinite;
  vertical-align: middle;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.security-tip {
  margin-top: 16rpx;
  color: #ff9500;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.leave-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leave-modal-box {
  background: #fff;
  border-radius: 18rpx;
  width: 520rpx;
  padding: 48rpx 0 0 0;
  box-shadow: 0 8rpx 32rpx 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}
.leave-modal-title {
  font-size: 32rpx;
  color: #222;
  font-weight: 500;
  margin-bottom: 18rpx;
}
.leave-modal-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}
.leave-modal-btns {
  display: flex;
  border-top: 1rpx solid #eee;
  height: 96rpx;
  margin-top: 0;
}
.leave-modal-btn {
  flex: 1;
  font-size: 32rpx;
  line-height: 96rpx;
  cursor: pointer;
}
.leave-modal-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}
.leave-modal-btn.confirm {
  color: #ff5134;
  font-weight: 500;
}
.enjoy-card-section {
  background: #fff;
  border-radius: 20rpx;
  margin: 32rpx 24rpx 0 24rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.02);
  padding: 32rpx;
  transition: box-shadow 0.2s;
  border: 2rpx solid #fff;
}
.enjoy-card-stage {
  width: 300rpx;
  height: 100rpx;
  box-sizing: border-box;
  background: #f7f7f7;
  border-radius: 12rpx;
  padding: 20rpx 26rpx 0 26rpx;
  cursor: pointer;
  border: 2rpx solid #f7f7f7;
  min-width: 160rpx;
  text-align: center;
  position: relative;
  .enjoy-card-amount {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    text-align: left;
  }
  .enjoy-card-term {
    font-weight: 400;
    font-size: 20rpx;
    color: #333333;
    text-align: right;
  }
  .enjoy-card-rate {
    position: absolute;
    width: 44rpx;
    height: 24rpx;
    background: #ff5134;
    top: 0;
    right: 0;
    font-weight: 400;
    font-size: 16rpx;
    color: #ffffff;
    border-top-right-radius: 12rpx;
    border-bottom-left-radius: 12rpx;
  }
}
.enjoy-card-stage.active {
  background: #fff7f5;
  border: 2rpx solid #ffcbc5;
  color: #ff5a00;
}
.other-pay-toggle {
  //   border-radius: 12rpx;
  padding: 32rpx 32rpx 32rpx 32rpx;
  //   margin: 0 24rpx 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  color: #222;
  cursor: pointer;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.02);
}
.protocol-check {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 24rpx;
  color: #333;
  margin: 10rpx 20rpx 0 60rpx;
  .protocol-radio {
    width: 28rpx;
    height: 28rpx;
    border-radius: 50%;
    border: 2rpx solid #ff5a00;
    display: inline-block;
    margin-right: 12rpx;
    position: relative;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s;
    &.checked {
      background: #ff5a00;
      border-color: #ff5a00;
    }
    &.checked::after {
      content: "";
      position: absolute;
      left: 7rpx;
      top: 3rpx;
      width: 10rpx;
      height: 16rpx;
      border: solid #fff;
      border-width: 0 4rpx 4rpx 0;
      border-radius: 2rpx;
      transform: rotate(45deg);
      display: block;
    }
  }
  .protocol-link {
    color: #ff5a00;
    margin-left: 4rpx;
    cursor: pointer;
    // text-decoration: underline;
  }
}
.credit-modal {
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
  min-height: 400rpx;
  .credit-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 32rpx 32rpx 0 32rpx;
    border-radius: 16rpx 16rpx 0 0;
  }
  .credit-modal-summary {
    padding: 0 32rpx 24rpx 32rpx;
    border-bottom: 1rpx solid #f2f2f2;
    margin-bottom: 0;
    div {
      line-height: 1.6;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
    }
    .credit-modal-amount {
      font-weight: 500;
      font-size: 48rpx;
      color: #333333;
    }
    .credit-modal-rate {
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
    }
  }
  .credit-modal-list {
    display: flex;
    flex-direction: column;
    padding: 32rpx 32rpx 0 32rpx;
    .credit-modal-item {
      display: flex;
      align-items: stretch;
      justify-content: flex-start;
      position: relative;
      //   min-height: 60rpx;
      //   padding: 18rpx 0;
      .credit-modal-item-left {
        // width: 200rpx;
        text-align: left;
        margin-bottom: 32rpx;
        .credit-modal-item-title {
          font-size: 32rpx;
          color: #000;
          font-weight: 400;
        }
        .credit-modal-item-date {
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
        }
      }
      .credit-modal-item-timeline {
        width: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        justify-content: flex-start;
        top: 40rpx;
        margin: 0 20rpx;
        .credit-dot {
          width: 12rpx;
          height: 12rpx;
          background: #1ec05f;
          border-radius: 50%;
          margin: 0;
          z-index: 1;
          position: relative;
        }
        .credit-line {
          width: 2rpx;
          background: #e5e5e5;
          flex: 1 1 auto;
          min-height: 32rpx;
          margin-top: 0;
          margin-bottom: 0;
        }
      }
      .credit-modal-item-right {
        flex: 1;
        text-align: left;
        // margin-left: 20rpx;
        .credit-modal-item-amount {
          font-weight: 400;
          font-size: 32rpx;
          color: #000000;
        }
        .credit-modal-item-detail {
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}
.sms-modal {
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  min-width: 540rpx;
}
.sms-header {
  position: relative;
  text-align: center;
  margin-bottom: 20rpx;
}
.sms-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.sms-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 26rpx;
  text-align: center;
}
.sms-row {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 40rpx;
}
.sms-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28rpx;
  color: #222;
  padding: 20rpx 0;
}
.sms-placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.sms-get {
  color: #ff5134;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.sms-get.disabled {
  color: #ccc;
}
.sms-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50rpx;
  border: none;
  margin-top: 20rpx;
  font-weight: 400;
}
</style>
