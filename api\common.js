/**
 * 公共API
 */
import { http, Method } from "@/utils/request.js";
import api from "@/config/api.js";

/**
 * 获取图片验证码
 */
export function getCaptcha() {
  return http.request({
    url: `${api.common}/common/captcha/getCaptcha`,
    method: Method.GET,
    responseType: 'arraybuffer'
  });
}


/**
 * 获取图片验证码校验
 */
export function checkGraphicCaptcha(type, pictureCode) {
  return http.request({
    url: `${api.common}/common/captcha/check/${type}`,
    method: Method.GET,
    data: { pictureCode },
    message: false,
  });
}

/**
 * 获取地区数据
 * @param id
 */
export function getRegionsById(id = 0) {
  return http.request({
    url: `${api.common}/common/region/item/${id}`,
    method: Method.GET,
    message: false,
  });
}

// 获取IM接口前缀
export function getIMDetail() {
  return http.request({
    url: `${api.common}/IM`,
    method: Method.GET,
    message: false,
  });
}

/**
 * 文件上传地址
 * @type {string}
 */
export const upload = api.common + "/common/upload/file";

/**
 * 获取多个字典项
 * @param {Array<string>} types - 字典类型数组，如 ["education","marriage_status"]
 */
export function getDictManyList(types) {
  return http.request({
    url: `/inst/api/dict/many/list`,
    method: Method.POST,
    data: types,
    message: false,
  });
}

/**
 * 获取银行卡验证码
 * @param {Object} params { bankCode, mobile, cardNumber, clientType }
 */
export function getBankCardAuthCode(params) {
  return http.request({
    url: '/inst/api/bankUser/authSign',
    method: Method.POST,
    data: params,
    message: false,
  });
}

/**
 * 新增绑卡用户
 * @param {Object} params { cardNumber, verifyCode, clientType }
 */
export function addBankUser(params) {
  return http.request({
    url: '/inst/api/bankUser/add',
    method: Method.POST,
    data: params,
    message: false,
  });
}

// 检查臻享卡显示状态
export function checkEnjoyCardStatus() {
  return http.request({
    url: 'http://192.168.1.33:9301/inst/api/getHomeCardInfo',
    method: Method.POST,
    message: false,
  });
}

// 获取臻享卡分期预信息
export function getLoanStagingPreInfo(params) {
  return http.request({
    url: '/inst/api/loan/staging/preInfo',
    method: Method.GET,
    data: params,
    message: false
  });
}
